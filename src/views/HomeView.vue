<template>
  <div class="home-view">
    <!-- 顶部Header -->
    <Header />

    <!-- 侧边栏导航 -->
    <SidebarNavigation :current-slide="currentSlide" :active-tab="activeTab" :main-swiper-instance="mainSwiperInstance"
      @slide-change="onSlideChange" @tab-change="switchTab" />

    <!-- 主要垂直滚屏容器 -->
    <VerticalSwiper custom-class="main-swiper" @slide-change="onSlideChange" @swiper-ready="onMainSwiperReady">
      <!-- 第1屏 - 首页 -->
      <swiper-slide class="slide-item slide-1">
        <div class="slide-content">

          <!-- 预约按钮 -->
          <div class="reservation-btn">
            <WebM class="store-button breathe" :src="WebMReservation" @click="scrollToNextSlide" />
          </div>

          <!-- 居中的应用商店按钮组 -->
          <div class="app-store-buttons">
            <WebM class="store-button breathe" :src="WebMGooglePlay" @click="handleStoreClick('google')" />
            <WebM class="store-button breathe" :src="WebMAppleStore" @click="handleStoreClick('apple')" />
            <WebM class="store-button breathe" :src="WebMOneStore" @click="handleStoreClick('one')" />
            <WebM class="store-button breathe" :src="WebMSamsungStore" @click="handleStoreClick('samsung')" />
          </div>

          <!-- 底部滚动按钮 -->
          <div class="scroll-down-wrapper">
            <img src="@/assets/imgs/p1/scroll-down.png" alt="向下滚动" class="scroll-down-button bounce"
              @click="scrollToNextSlide" />
          </div>
        </div>
      </swiper-slide>

      <!-- 第2屏 - 预约 -->
      <swiper-slide class="slide-item slide-2">
        <WebM class="particle" :src="WebMParticle" />
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p2/title.png" alt="">
          </div>
          <div class="content">
            <!-- 预约奖励 -->
            <div class="rewards">
              <WebM class="reward" :src="WebMReservationReward1" />
              <WebM class="reward" :src="WebMReservationReward2" />
            </div>
            <!-- 平台选择按钮 -->
            <div class="platform-buttons">
              <button class="platform-btn aos-btn" :class="{ active: selectedPlatform === 'aos' }"
                @click="selectedPlatform = 'aos'">
              </button>
              <button class="platform-btn ios-btn" :class="{ active: selectedPlatform === 'ios' }"
                @click="selectedPlatform = 'ios'">
              </button>
            </div>
            <div class="phone-input">
              <PhoneInput v-model="phoneNumber" />
            </div>
            <div class="rule-btn">
              <img src="@/assets/imgs/p2/rule-btn.png" alt="">
            </div>
            <div class="reservation-btn">
              <WebM class="reservation breathe" :src="WebMP2ReservationBtn" />
            </div>
            <div class="big-reward">
              <WebM class="reward" :src="WebMReservationReward3" />
            </div>
            <div class="store-buttons">
              <WebM class="store-button breathe" :src="WebMGooglePlay" @click="handleStoreClick('google')" />
              <WebM class="store-button breathe" :src="WebMAppleStore" @click="handleStoreClick('apple')" />
            </div>
            <div class="community-btn">
              <WebM class="community breathe" :src="WebMP2CommunityBtn" />
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第3屏 - 抽奖 -->
      <swiper-slide class="slide-item slide-3">
        <WebM class="particle" :src="WebMParticle" />
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p3/title.png" alt="">
          </div>
          <div class="lottery">
            <WebM :src="WebMLottery" />
            <div class="lottery-btn">
              <img class="breathe" src="@/assets/imgs/p3/lottery-btn.png" alt="">
            </div>
          </div>
          <div class="rule-btn">
            <img src="@/assets/imgs/p3/rule-btn.png" alt="">
          </div>
        </div>
      </swiper-slide>

      <!-- 第4屏 - 里程碑 -->
      <swiper-slide class="slide-item slide-4">
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p4/title.png">
          </div>

          <div class="reservation-count">
            <img src="@/assets/imgs/p4/reservation-count.png" alt="">
            <div class="count">{{ reservationCount }}</div>
          </div>

          <div class="milestone-container">
            <!-- 动态里程碑图片 -->
            <div class="milestone-image">
              <img :src="milestoneImagesList[currentLevel]" :alt="`里程碑等级 ${currentLevel}`">
              <!-- 奖品图片 -->
              <div v-if="currentLevel === 5" class="prize-image shine">
                <img class="breathe" src="@/assets/imgs/p4/prize.png" alt="奖品">
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第5屏 - 职业介绍 -->
      <swiper-slide class="slide-item slide-5">
        <WebM class="particle" :src="WebMParticle" />

        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p5/title.png">
          </div>

          <!-- 职业介绍主要内容 -->
          <div class="character-introduction">
            <!-- 左侧内容区域  -->
            <div class="left-section">
              <!-- 角色故事区域 -->
              <div class="character-story">
                <transition name="fade" mode="out-in">
                  <img :key="selectedCharacter" :src="characterStoryImage" :alt="`角色${selectedCharacter}故事背景`"
                    class="story-image" />
                </transition>
              </div>

              <!-- 职业切换按钮组 -->
              <div class="character-role-buttons">
                <button v-for="character in characters" :key="character.id"
                  :class="['role-btn', { active: selectedCharacter === character.id }]"
                  @click="selectCharacter(character.id)">
                  <img :src="getRoleButtonImage(character.id)" :alt="`角色${character.id}职业图标`" class="role-icon" />
                </button>
              </div>
            </div>

            <!-- 右侧内容区域  -->
            <div class="right-section">
              <!-- 角色立绘 -->
              <div class="character-portrait">
                <WebM class="character-turntable" :src="WebMCharacterTurntable" />
                <transition name="slide-fade" mode="out-in">
                  <img :key="selectedCharacter" :src="characterPortraitImage" :alt="`角色${selectedCharacter}立绘`"
                    class="portrait-image" />
                </transition>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第6屏 - 公会 -->
      <swiper-slide class="slide-item slide-6">
        <div class="slide-content">
          <div class="title">
            <img src="@/assets/imgs/p6/title.png">
          </div>

          <div class="btns">
            <div class="btn create">
              <img src="@/assets/imgs/p6/create-guild-btn.png" alt="">
            </div>
            <div class="btn join" @click="isJoinGuild = !isJoinGuild">
              <img src="@/assets/imgs/p6/join-guild-btn.png" alt="">
            </div>
          </div>

          <div class="content">
            <div class="guild-list">
              <div class="guild-item" v-for="guild in currentPageGuilds" :key="guild.id">
                <div class="info">
                  <span class="name">{{ guild.name }}</span>
                  <span class="link">{{ guild.link }}</span>
                </div>
                <transition name="slide-fade" mode="out-in">
                  <div v-if="isJoinGuild" class="btn" :class="{ full: guild.status === 'full' }"></div>
                </transition>
              </div>
            </div>

            <!-- 分页控件 -->
            <div class="pagination">
              <button class="pagination-btn prev-btn" :disabled="isPrevDisabled" @click="goToPrevPage">
                <img src="@/assets/imgs/p6/right-arrow.png" alt="上一页" class="arrow-icon prev-arrow" />
              </button>

              <!-- 页码按钮组 -->
              <div class="page-numbers">
                <span v-for="page in totalPages" :key="page" class="page-number-btn"
                  :class="{ active: currentPage === page }" @click="goToPage(page)">
                  {{ page }}
                </span>
              </div>

              <button class="pagination-btn next-btn" :disabled="isNextDisabled" @click="goToNextPage">
                <img src="@/assets/imgs/p6/right-arrow.png" alt="下一页" class="arrow-icon next-arrow" />
              </button>
            </div>
          </div>
        </div>
      </swiper-slide>

      <!-- 第7屏 - 游戏世界观介绍 -->
      <swiper-slide class="slide-item slide-7">
        <div class="slide-content world-view-container">

          <!-- Tab 切换导航 -->
          <div class="tab-navigation">
            <button :class="['tab-btn', 'tab1', { active: activeTab === 'tab1' }]" @click="switchTab('tab1')">
            </button>
            <button :class="['tab-btn', 'tab2', { active: activeTab === 'tab2' }]" @click="switchTab('tab2')">
            </button>
          </div>

          <!-- Tab 内容区域 -->
          <div class="tab-content">
            <!-- Tab Swiper: 使用fade效果的全屏切换 -->
            <Swiper :modules="[EffectFade]" effect="fade" :fade-effect="{ crossFade: true }" :allow-touch-move="false"
              :navigation="false" :pagination="false" :scrollbar="false" class="tab-swiper" @swiper="onTabSwiperReady"
              @slide-change="onTabSwiperChange">
              <!-- Tab1: 水平全屏轮播 -->
              <swiper-slide class="tab-panel tab1-panel">
                <WorldViewSwiper />
              </swiper-slide>

              <!-- Tab2: 固定背景 + 居中轮播 -->
              <swiper-slide class="tab-panel tab2-panel">
                <StorySwiper />
              </swiper-slide>
            </Swiper>
          </div>
        </div>
      </swiper-slide>
    </VerticalSwiper>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectFade } from 'swiper/modules'
// 导入自定义组件
import WebM from '@/components/WebM.vue'
import Header from '@/components/Header.vue'
import PhoneInput from '@/components/PhoneInput.vue'
import VerticalSwiper from '@/components/swiper/VerticalSwiper.vue'
import WorldViewSwiper from '@/components/swiper/WorldViewSwiper.vue'
import StorySwiper from '@/components/swiper/StorySwiper.vue'
import SidebarNavigation from '@/components/SidebarNavigation.vue'
// WebM
import WebMParticle from '@/assets/imgs/webm/particle.webm'
import WebMReservation from '@/assets/imgs/webm/reservation.webm'
import WebMAppleStore from '@/assets/imgs/webm/apple-store.webm'
import WebMGooglePlay from '@/assets/imgs/webm/google-play.webm'
import WebMOneStore from '@/assets/imgs/webm/one-store.webm'
import WebMSamsungStore from '@/assets/imgs/webm/samsung-store.webm'
import WebMP2ReservationBtn from '@/assets/imgs/webm/p2-reservation-btn.webm'
import WebMP2CommunityBtn from '@/assets/imgs/webm/p2-community-btn.webm'
import WebMReservationReward1 from '@/assets/imgs/webm/reservation-reward1.webm'
import WebMReservationReward2 from '@/assets/imgs/webm/reservation-reward2.webm'
import WebMReservationReward3 from '@/assets/imgs/webm/reservation-reward3.webm'
import WebMLottery from '@/assets/imgs/webm/lottery.webm'
import WebMCharacterTurntable from '@/assets/imgs/webm/character-turntable.webm'

// Hooks
import { useDeviceDetection } from '@/hooks/useDeviceDetection'

// 批量导入里程碑图片
const milestoneImagesRaw = import.meta.glob('@/assets/imgs/p4/lv*.png', { eager: true })
const milestoneImagesList = computed(() => {
  return Object.values(milestoneImagesRaw).map((item: any) => item.default)
})

// 响应式数据
const currentSlide = ref(0)
const activeTab = ref('tab1')
const mainSwiperInstance = ref<any>(null)
const tabSwiperInstance = ref<any>(null)

// 设备检测
const { deviceType } = useDeviceDetection()

// 平台选择 - 根据设备类型初始化
const selectedPlatform = ref<'aos' | 'ios'>(deviceType.value === 'ios' ? 'ios' : 'aos')

// 电话号码相关
const phoneNumber = ref('')

// 第4屏里程碑等级控制
const currentLevel = ref(0) // 0-5 对应 lv0.png 到 lv5.png
const reservationCount = ref(123456)

// 第5屏职业介绍相关数据
const selectedCharacter = ref(1) // 当前选中的角色ID

// 角色数据数组
const characters = ref([
  { id: 1, name: '角色1' },
  { id: 2, name: '角色2' },
  { id: 3, name: '角色3' },
  { id: 4, name: '角色4' },
  { id: 5, name: '角色5' }
])

// 第6屏公会列表相关数据
// 公会数据数组 
const guilds = ref([
  { id: 1, name: '龙之传说', link: 'https://www.example1.com', status: 'available' },
  { id: 2, name: '星辰公会', link: 'https://www.example2.com', status: 'full' },
])
const isJoinGuild = ref(false)
const currentPage = ref(1) // 当前页码
const pageSize = 4 // 每页显示数量
const totalPages = computed(() => Math.ceil(guilds.value.length / pageSize)) // 总页数



// 计算当前页显示的公会列表
const currentPageGuilds = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize
  const endIndex = startIndex + pageSize
  return guilds.value.slice(startIndex, endIndex)
})

// 分页控制方法
const goToPrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// 判断按钮是否禁用
const isPrevDisabled = computed(() => currentPage.value === 1)
const isNextDisabled = computed(() => currentPage.value === totalPages.value)

// 跳转到指定页面
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 计算属性：动态生成图片路径
const characterStoryImage = computed(() => {
  return new URL(`../assets/imgs/p5/character-story/${selectedCharacter.value}.png`, import.meta.url).href
})

const characterPortraitImage = computed(() => {
  return new URL(`../assets/imgs/p5/character/${selectedCharacter.value}.png`, import.meta.url).href
})

// 获取职业按钮图片的方法
const getRoleButtonImage = (characterId: number) => {
  const state = selectedCharacter.value === characterId ? 'active' : 'normal'
  return new URL(`../assets/imgs/p5/character-role/${state}/${characterId}.png`, import.meta.url).href
}

// 选择角色的方法
const selectCharacter = (characterId: number) => {
  selectedCharacter.value = characterId
}

// 主 Swiper 事件处理
const onSlideChange = (index: number) => {
  currentSlide.value = index
}

const onMainSwiperReady = (swiper: any) => {
  mainSwiperInstance.value = swiper
}

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab

  // 控制tab swiper切换
  if (tabSwiperInstance.value) {
    const slideIndex = tab === 'tab1' ? 0 : 1
    tabSwiperInstance.value.slideTo(slideIndex)
  }
}

// Tab Swiper 事件处理
const onTabSwiperReady = (swiper: any) => {
  tabSwiperInstance.value = swiper
}

const onTabSwiperChange = (swiper: any) => {
  const currentIndex = swiper.activeIndex
  activeTab.value = currentIndex === 0 ? 'tab1' : 'tab2'
}

// 应用商店按钮点击处理
const handleStoreClick = (store: string) => {
  console.log(`点击了${store}应用商店`)
}

// 滚动到下一屏
const scrollToNextSlide = () => {
  if (mainSwiperInstance.value) {
    mainSwiperInstance.value.slideTo(1) // 导航到第2屏
  }
}
</script>

<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  overflow: hidden;
  position: relative;
}

// 粒子效果
.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.slide-item {
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .title {
    position: absolute;
    top: 10px;
  }

  // 为每个屏幕设置背景图
  &.slide-1 {
    background-image: url('@/assets/imgs/bg/p1.jpg');
  }

  &.slide-2 {
    background-image: url('@/assets/imgs/bg/p2.jpg');
  }

  &.slide-3 {
    background-image: url('@/assets/imgs/bg/p3.jpg');
  }

  &.slide-4 {
    background-image: url('@/assets/imgs/bg/p4.jpg');
  }

  &.slide-5 {
    background-image: url('@/assets/imgs/bg/p5.jpg');
  }

  &.slide-6 {
    background-image: url('@/assets/imgs/bg/p6.jpg');
  }

  &.slide-7 {
    background-color: #000;
  }
}

.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 第1屏
.slide-1 {
  .reservation-btn {
    position: absolute;
    right: 24%;
    bottom: 24%;
    cursor: pointer;
  }

  .app-store-buttons {
    position: absolute;
    bottom: 130px;
    left: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    transform: translateX(-50%);

    .store-button {
      width: 210px;
      height: 65px;
      cursor: pointer;
    }

    @media (max-width: 480px) {}
  }

  // 滚动按钮样式
  .scroll-down-wrapper {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);

    .scroll-down-button {
      width: 121px;
      height: auto;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.3));
      }
    }

    @media (max-width: 768px) {
      bottom: 30px;

      .scroll-down-button {
        width: 50px;
      }
    }
  }
}

// 第2屏
.slide-2 {

  .title {
    position: absolute;
    top: 10px;
  }

  .content {
    position: absolute;
    top: 170px;
    margin-left: -50px;
    width: 1239px;
    height: 776px;
    background: url(@/assets/imgs/p2/box-bg.png);
    display: flex;
    align-items: center;
    justify-content: center;

    .rewards {
      position: absolute;
      top: 130px;
      left: 100px;
      display: flex;
      align-items: center;

      .reward {
        margin: 0 -85px;
      }
    }

    .platform-buttons {
      position: absolute;
      top: 470px;
      left: 290px;
      display: flex;
      gap: 80px;
      align-items: center;
      justify-content: center;

      .platform-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 0;
        width: 53px;
        height: 16px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;

        &.aos-btn {
          background-image: url(@/assets/imgs/p2/aos.png);

          &.active {
            background-image: url(@/assets/imgs/p2/aos-active.png);
          }
        }

        &.ios-btn {
          background-image: url(@/assets/imgs/p2/ios.png);

          &.active {
            background-image: url(@/assets/imgs/p2/ios-active.png);
          }
        }
      }
    }

    .phone-input {
      position: absolute;
      top: 500px;
      left: 170px;
      width: 420px;
    }

    .rule-btn {
      position: absolute;
      top: 556px;
      left: 540px;
      cursor: pointer;
    }

    .reservation-btn {
      position: absolute;
      bottom: 85px;
      left: 235px;
      cursor: pointer;
    }

    .big-reward {
      position: absolute;
      top: 110px;
      right: 70px;
    }

    .store-buttons {
      position: absolute;
      right: 135px;
      top: 375px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .store-button {
        width: 190px;
        cursor: pointer;
      }
    }

    .community-btn {
      position: absolute;
      bottom: 85px;
      right: 180px;
      cursor: pointer;
    }
  }
}

.slide-3 {
  .title {
    position: absolute;
    top: 10px;
    z-index: 1;
  }

  .lottery {
    position: absolute;
    top: 10px;
    width: 1920px;

    .lottery-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      cursor: pointer;
      z-index: 1;
    }
  }

  .rule-btn {
    position: absolute;
    bottom: 50px;
    cursor: pointer;
  }
}

// 第4屏
.slide-4 {
  .title {
    position: absolute;
    top: 10px;
  }

  .reservation-count {
    position: absolute;
    top: 220px;
    display: flex;
    align-items: center;
    justify-content: center;

    .count {
      margin-left: 10px;
      font-size: 60px;
      font-weight: bold;
      background: linear-gradient(180deg, #fcfdfd 0%, #fefece 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      letter-spacing: 2px;
      position: relative;
      filter: drop-shadow(0 0 5px #333);

      /* 为不支持背景裁剪的浏览器提供备用方案 */
      color: #fcfdfd;
    }
  }

  .milestone-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 200px 0 0 20px;

    .milestone-image {
      position: relative;

      .prize-image {
        position: absolute;
        top: 240px;
        right: 25px;
        cursor: pointer;
      }
    }
  }
}

// 第5屏 - 职业介绍
.slide-5 {
  .title {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
  }

  .character-introduction {
    position: absolute;
    top: 120px;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 8%;

    // 左侧内容区域 
    .left-section {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .character-story {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 30px;

        .story-image {
          width: 600px;
          height: 450px;
          object-fit: contain;
        }
      }

      .character-role-buttons {
        display: flex;
        justify-content: center;
        gap: 30px;

        .role-btn {
          background: none;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &:hover {
            transform: scale(1.05);
            filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
          }

          .role-icon {
            width: 119px;
            object-fit: contain;
            transition: all 0.3s ease;
          }
        }
      }
    }

    // 右侧内容区域 
    .right-section {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 20px;

      .character-portrait {
        position: relative;
        width: 500px;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .character-turntable {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 800px;
          height: 800px;
          transform: translate(-50%, -50%);
        }

        .portrait-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.4));
        }
      }
    }
  }
}

// 第6屏
.slide-6 {
  .btns {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 100px 0 0;
    gap: 50px;

    .btn {
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: scale(1.05);
        filter: brightness(1.1) drop-shadow(0 0 10px #b68bc3);
      }

      &.join {
        &:hover {
          filter: brightness(1.1) drop-shadow(0 0 10px #b4e0ff);
        }
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-top: 100px;
    width: 1036px;
    height: 588px;
    background: url(@/assets/imgs/p6/box-bg.png);
    color: #fff;

    .guild-list {
      display: flex;
      align-items: center;
      flex-direction: column;
      min-height: 300px;
      gap: 30px;
      margin-bottom: 40px;
    }

    .guild-item {
      display: flex;
      align-items: center;
      gap: 20px;

      .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 817px;
        height: 51px;
        background: url(@/assets/imgs/p6/guild-bg.png);

        span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 49%;
          height: 100%;
          font-size: 18px;
        }

        .link {
          width: 48%;
        }
      }

      .btn {
        width: 100px;
        height: 35px;
        background: url(@/assets/imgs/p6/join-btn.png);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover:not(.full) {
          transform: scale(1.05);
          filter: brightness(1.1);
        }

        &.full {
          background-image: url(@/assets/imgs/p6/can-not-join-btn.png);
          cursor: not-allowed;
        }
      }
    }

    // 分页控件样式
    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 30px;
      margin-top: 20px;

      .pagination-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        padding: 8px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(:disabled) {
          transform: scale(1.1);
        }

        &:disabled {
          opacity: 0.3;
          cursor: not-allowed;
        }

        .arrow-icon {
          width: 21px;
          height: 9px;
          transition: all 0.3s ease;

          &.prev-arrow {
            transform: scaleX(-1);
          }

          &.next-arrow {
            transform: scaleX(1);
          }
        }
      }

      // 页码按钮组样式
      .page-numbers {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;

        .page-number-btn {
          width: 40px;
          height: 40px;
          color: #a7a7a7;
          cursor: pointer;
          font-size: 24px;
          font-weight: bold;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: #fff;
            transform: scale(1.2);
          }

          &.active {
            color: #fff;
            filter: drop-shadow(0 0 10px #fff);
          }
        }
      }
    }
  }
}

// 第7屏
.slide-7 {
  .world-view-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 0;
    box-sizing: border-box;
    position: relative;

    .tab-navigation {
      display: flex;
      gap: 2rem;
      margin: 2rem 0;
      z-index: 100;
      position: absolute;
      top: 2rem;
      left: 50%;
      transform: translateX(-50%);

      .tab-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        background-repeat: no-repeat;
        background-position: center;
        width: 351px;
        height: 89px;

        .active {
          width: 345px;
          height: 184px;
        }

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.1);
        }

        &.tab1 {
          background-image: url(@/assets/imgs/p7/tab1.png);

          &.active {
            background-image: url(@/assets/imgs/p7/tab1-active.png);
          }
        }

        &.tab2 {
          background-image: url(@/assets/imgs/p7/tab2.png);

          &.active {
            background-image: url(@/assets/imgs/p7/tab2-active.png);
          }
        }
      }
    }

    .tab-content {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    // Tab Swiper 样式
    .tab-swiper {
      width: 100%;
      height: 100%;

      .swiper-wrapper {
        width: 100%;
        height: 100%;
      }

      .swiper-slide {
        width: 100%;
        height: 100%;
        position: relative;
      }
    }

    .tab-panel {
      width: 100%;
      height: 100%;
      position: relative;

      // Tab1 - 水平全屏轮播样式
      &.tab1-panel {
        // 确保Tab1面板占据整个第7屏
        width: 100vw;
        height: 100vh;
        z-index: 10;
      }

      // Tab2 - 固定背景样式
      &.tab2-panel {
        background-image: url('@/assets/imgs/bg/p7-2.jpg');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100vh;
        z-index: 10;
      }
    }
  }
}

// 过渡动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.6s ease;
}

.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

// 响应式设计
@media (max-width: 1200px) {
  .slide-5 {
    .character-introduction {
      padding: 0 20px;

      .left-section {
        .character-role-buttons {
          gap: 15px;

          .role-btn .role-icon {
            width: 60px;
            height: 60px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .slide-5 {
    .character-introduction {
      flex-direction: column;
      padding: 0 15px;

      .left-section,
      .right-section {
        width: 100%;
        padding: 0;
      }

      .left-section {
        height: 50%;
        margin-bottom: 20px;

        .character-role-buttons {
          gap: 10px;

          .role-btn .role-icon {
            width: 50px;
            height: 50px;
          }
        }
      }

      .right-section {
        height: 50%;
      }
    }
  }
}
</style>