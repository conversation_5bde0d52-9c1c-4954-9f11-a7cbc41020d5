<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公会列表测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .guild-list {
            margin-bottom: 30px;
        }
        .guild-item {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 600px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }
        .btn {
            width: 100px;
            height: 35px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn.full {
            background: #f44336;
            cursor: not-allowed;
        }
        .pagination {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
        }
        .pagination-btn {
            background: #333;
            border: 1px solid #555;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .pagination-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
        .page-info {
            font-size: 18px;
            font-weight: bold;
        }
        .current-page {
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>公会列表分页测试</h1>
        
        <div class="guild-list" id="guildList">
            <!-- 动态生成的公会列表 -->
        </div>
        
        <div class="pagination">
            <button class="pagination-btn" id="prevBtn" onclick="goToPrevPage()">上一页</button>
            <div class="page-info">
                <span class="current-page" id="currentPage">1</span>
                <span>/</span>
                <span id="totalPages">5</span>
            </div>
            <button class="pagination-btn" id="nextBtn" onclick="goToNextPage()">下一页</button>
        </div>
    </div>

    <script>
        // 公会数据
        const guilds = [
            // 第1页
            { id: 1, name: '龙之传说', link: 'https://www.example1.com', status: 'available' },
            { id: 2, name: '星辰公会', link: 'https://www.example2.com', status: 'full' },
            { id: 3, name: '风暴战士', link: 'https://www.example3.com', status: 'available' },
            { id: 4, name: '光明守护', link: 'https://www.example4.com', status: 'full' },
            // 第2页
            { id: 5, name: '暗影联盟', link: 'https://www.example5.com', status: 'available' },
            { id: 6, name: '烈火军团', link: 'https://www.example6.com', status: 'full' },
            { id: 7, name: '冰霜之心', link: 'https://www.example7.com', status: 'available' },
            { id: 8, name: '雷电之怒', link: 'https://www.example8.com', status: 'full' },
            // 第3页
            { id: 9, name: '圣光骑士', link: 'https://www.example9.com', status: 'available' },
            { id: 10, name: '血色玫瑰', link: 'https://www.example10.com', status: 'full' },
            { id: 11, name: '钢铁兄弟', link: 'https://www.example11.com', status: 'available' },
            { id: 12, name: '魔法学院', link: 'https://www.example12.com', status: 'full' },
            // 第4页
            { id: 13, name: '战神殿堂', link: 'https://www.example13.com', status: 'available' },
            { id: 14, name: '月影公会', link: 'https://www.example14.com', status: 'full' },
            { id: 15, name: '天空之城', link: 'https://www.example15.com', status: 'available' },
            { id: 16, name: '地狱火焰', link: 'https://www.example16.com', status: 'full' },
            // 第5页
            { id: 17, name: '永恒之光', link: 'https://www.example17.com', status: 'available' },
            { id: 18, name: '暴风雪', link: 'https://www.example18.com', status: 'full' },
            { id: 19, name: '黄金军团', link: 'https://www.example19.com', status: 'available' },
            { id: 20, name: '终极战队', link: 'https://www.example20.com', status: 'full' }
        ];

        let currentPage = 1;
        const pageSize = 4;
        const totalPages = 5;

        function getCurrentPageGuilds() {
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            return guilds.slice(startIndex, endIndex);
        }

        function renderGuildList() {
            const guildList = document.getElementById('guildList');
            const currentGuilds = getCurrentPageGuilds();
            
            guildList.innerHTML = currentGuilds.map(guild => `
                <div class="guild-item">
                    <div class="info">
                        <span class="name">${guild.name}</span>
                        <span class="link">${guild.link}</span>
                    </div>
                    <button class="btn ${guild.status === 'full' ? 'full' : ''}">${guild.status === 'full' ? '满员' : '可加入'}</button>
                </div>
            `).join('');
        }

        function updatePagination() {
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('prevBtn').disabled = currentPage === 1;
            document.getElementById('nextBtn').disabled = currentPage === totalPages;
        }

        function goToPrevPage() {
            if (currentPage > 1) {
                currentPage--;
                renderGuildList();
                updatePagination();
            }
        }

        function goToNextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                renderGuildList();
                updatePagination();
            }
        }

        // 初始化
        renderGuildList();
        updatePagination();
    </script>
</body>
</html>
